# 山界规则修正

## 问题描述
用户指出了一个重要的军旗游戏规则问题：**6,1和7,1不通，6,3和7,3不能直接通，隔着山界**。

这是军旗游戏中的基本规则：**山界（楚河汉界）**位于第5行和第6行之间，棋子不能直接跨越山界，只能通过特定的铁路通道。

## 山界规则

### 🏔️ **山界位置**
- **位置**: 第5行和第6行之间（行索引5和6之间）
- **作用**: 阻止棋子直接跨越，类似象棋中的楚河汉界

### 🚫 **禁止的移动**
棋子不能直接从以下位置跨越山界：
- 第6行 ↔ 第7行（玩家区域）
- 第4行 ↔ 第5行（AI区域）

### ✅ **允许的跨越方式**
只能通过**铁路通道**跨越山界：
- **水平铁路**: 第5行和第6行是水平铁路线
- **垂直铁路**: 第0列和第4列是垂直铁路线
- **工兵特权**: 工兵可以沿铁路自由移动，包括跨越山界

## 修正内容

### 🔧 **删除的违规连接**

#### 玩家区域（第6-7行之间）
删除了以下违反山界规则的显式对角连接：
```javascript
// 已删除的违规连接
'8,0-7,1', '7,1-8,0', // 跨越山界
'8,4-7,3', '7,3-8,4', // 跨越山界  
'6,4-7,3', '7,3-6,4', // 跨越山界
'7,1-6,0', '6,0-7,1', // 跨越山界
'7,1-6,2', '6,2-7,1', // 跨越山界
'7,3-6,2', '6,2-7,3', // 跨越山界
```

#### AI区域（第4-5行之间）
删除了以下违反山界规则的显式对角连接：
```javascript
// 已删除的违规连接
'3,0-4,1', '4,1-3,0', // 跨越山界
'3,4-4,3', '4,3-3,4', // 跨越山界
'5,0-4,1', '4,1-5,0', // 跨越山界
```

### ✅ **保留的合法连接**

#### 营地内部连接（不跨越山界）
保留了同一区域内的营地连接：
```javascript
// 玩家区域内部连接
'8,0-9,1', '9,1-8,0', // (9,1) is camp
'8,2-9,1', '9,1-8,2', // (8,2) and (9,1) are camps
'8,2-9,3', '9,3-8,2', // (8,2) and (9,3) are camps
'8,4-9,3', '9,3-8,4', // (9,3) is camp

// AI区域内部连接
'3,0-2,1', '2,1-3,0', // (2,1) is camp
'3,2-2,1', '2,1-3,2', // (3,2) and (2,1) are camps
'3,2-2,3', '2,3-3,2', // (3,2) and (2,3) are camps
'3,4-2,3', '2,3-3,4', // (2,3) is camp
```

#### 大本营连接
保留了到大本营的连接：
```javascript
// 到玩家大本营的连接
'10,0-11,1', '10,2-11,1', '10,2-11,3', '10,4-11,3'

// 到AI大本营的连接  
'1,0-0,1', '1,2-0,1', '1,2-0,3', '1,4-0,3'
```

## 游戏影响

### 🎯 **战术变化**
1. **更真实的军旗体验**: 严格遵循传统军旗规则
2. **增加战略深度**: 山界成为天然屏障，需要更仔细的规划
3. **工兵价值提升**: 工兵成为跨越山界的重要棋子
4. **铁路控制重要性**: 控制铁路通道变得更加关键

### 🚀 **AI适应性**
- AI会自动适应新的移动规则
- 评估函数会重新计算位置价值
- 工兵在铁路位置的价值会被正确评估

### ✅ **规则合规性**
- 完全符合传统军旗游戏规则
- 山界成为真正的战略屏障
- 增加了游戏的挑战性和真实性

## 验证结果

### ✅ **移动规则检查**
- (6,1) 和 (7,1) 不能直接连通 ✅
- (6,3) 和 (7,3) 不能直接连通 ✅
- 只能通过铁路跨越山界 ✅
- 工兵可以沿铁路自由移动 ✅

### ✅ **语法检查**
- JavaScript语法正确 ✅
- 连接定义完整 ✅
- 游戏逻辑一致 ✅

现在军旗游戏完全遵循正确的山界规则，提供更加真实和具有挑战性的游戏体验！🏔️
